import pytest
import asyncio
from unittest.mock import patch, AsyncMock
from app.main import main

@pytest.mark.asyncio
async def test_main_creates_agent():
    with patch('app.main.p.Server') as mock_server:
        mock_server_instance = AsyncMock()
        mock_server.return_value.__aenter__.return_value = mock_server_instance
        
        await main()
        
        mock_server_instance.create_agent.assert_called_once_with(
            name="Son-Vidéo IA",
            description="Tu es un conseiller Son-Vidéo."
        )